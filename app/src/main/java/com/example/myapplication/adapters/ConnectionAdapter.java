package com.example.myapplication.adapters;

import android.content.Context;
import android.graphics.Color;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.models.DatabaseConnection;

import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView adapter for displaying database connections
 */
public class ConnectionAdapter extends RecyclerView.Adapter<ConnectionAdapter.ConnectionViewHolder> {
    
    private List<DatabaseConnection> connections;
    private Context context;
    private OnConnectionClickListener listener;
    
    public interface OnConnectionClickListener {
        void onConnectionClick(DatabaseConnection connection);
        void onConnectionMenuClick(DatabaseConnection connection, View view);
    }
    
    public ConnectionAdapter(Context context) {
        this.context = context;
        this.connections = new ArrayList<>();
    }
    
    public void setOnConnectionClickListener(OnConnectionClickListener listener) {
        this.listener = listener;
    }
    
    public void setConnections(List<DatabaseConnection> connections) {
        this.connections = connections != null ? connections : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    public void addConnection(DatabaseConnection connection) {
        connections.add(connection);
        notifyItemInserted(connections.size() - 1);
    }
    
    public void updateConnection(DatabaseConnection connection) {
        for (int i = 0; i < connections.size(); i++) {
            if (connections.get(i).getId().equals(connection.getId())) {
                connections.set(i, connection);
                notifyItemChanged(i);
                break;
            }
        }
    }
    
    public void removeConnection(String connectionId) {
        for (int i = 0; i < connections.size(); i++) {
            if (connections.get(i).getId().equals(connectionId)) {
                connections.remove(i);
                notifyItemRemoved(i);
                break;
            }
        }
    }
    
    @NonNull
    @Override
    public ConnectionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_connection, parent, false);
        return new ConnectionViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ConnectionViewHolder holder, int position) {
        DatabaseConnection connection = connections.get(position);
        holder.bind(connection);
    }
    
    @Override
    public int getItemCount() {
        return connections.size();
    }
    
    class ConnectionViewHolder extends RecyclerView.ViewHolder {
        private View colorIndicator;
        private ImageView ivConnectionIcon;
        private TextView tvConnectionName;
        private TextView tvConnectionDetails;
        private TextView tvConnectionStatus;
        private TextView tvLastConnected;
        private ImageView ivFavorite;
        private ImageView ivMenuAction;
        private View statusIndicator;
        
        public ConnectionViewHolder(@NonNull View itemView) {
            super(itemView);
            
            colorIndicator = itemView.findViewById(R.id.colorIndicator);
            ivConnectionIcon = itemView.findViewById(R.id.ivConnectionIcon);
            tvConnectionName = itemView.findViewById(R.id.tvConnectionName);
            tvConnectionDetails = itemView.findViewById(R.id.tvConnectionDetails);
            tvConnectionStatus = itemView.findViewById(R.id.tvConnectionStatus);
            tvLastConnected = itemView.findViewById(R.id.tvLastConnected);
            ivFavorite = itemView.findViewById(R.id.ivFavorite);
            ivMenuAction = itemView.findViewById(R.id.ivMenuAction);
            statusIndicator = itemView.findViewById(R.id.statusIndicator);
            
            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onConnectionClick(connections.get(position));
                    }
                }
            });
            
            ivMenuAction.setOnClickListener(v -> {
                if (listener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        listener.onConnectionMenuClick(connections.get(position), v);
                    }
                }
            });
        }
        
        public void bind(DatabaseConnection connection) {
            // Set connection name
            tvConnectionName.setText(connection.getDisplayName());
            
            // Set connection details
            String details = String.format("%s:%d", connection.getHost(), connection.getPort());
            if (connection.getDatabase() != null && !connection.getDatabase().isEmpty()) {
                details += "/" + connection.getDatabase();
            }
            tvConnectionDetails.setText(details);
            
            // Set color indicator
            try {
                int color = Color.parseColor(connection.getColor());
                colorIndicator.setBackgroundColor(color);
                ivConnectionIcon.setColorFilter(color);
            } catch (Exception e) {
                // Use default color if parsing fails
                colorIndicator.setBackgroundColor(context.getColor(R.color.primary_color));
                ivConnectionIcon.setColorFilter(context.getColor(R.color.primary_color));
            }
            
            // Set favorite indicator
            ivFavorite.setVisibility(connection.isFavorite() ? View.VISIBLE : View.GONE);
            
            // Set connection status (for now, all are disconnected)
            tvConnectionStatus.setText("Disconnected");
            statusIndicator.setBackgroundTintList(
                context.getColorStateList(R.color.disconnected_color)
            );
            
            // Set last connected time
            if (connection.getLastConnected() > 0) {
                CharSequence timeAgo = DateUtils.getRelativeTimeSpanString(
                    connection.getLastConnected(),
                    System.currentTimeMillis(),
                    DateUtils.MINUTE_IN_MILLIS,
                    DateUtils.FORMAT_ABBREV_RELATIVE
                );
                tvLastConnected.setText(timeAgo);
            } else {
                tvLastConnected.setText("Never");
            }
        }
    }
}
