package com.example.myapplication;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.adapters.ConnectionAdapter;
import com.example.myapplication.database.ConnectionManager;
import com.example.myapplication.models.DatabaseConnection;
import com.example.myapplication.utils.SampleDataGenerator;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

public class MainActivity extends AppCompatActivity implements ConnectionAdapter.OnConnectionClickListener {

    private MaterialToolbar toolbar;
    private MaterialCardView welcomeCard;
    private MaterialCardView cardNewConnection;
    private MaterialCardView cardQueryEditor;
    private RecyclerView recyclerViewConnections;
    private LinearLayout emptyStateLayout;
    private TextView tvRecentConnections;
    private FloatingActionButton fabAddConnection;

    private ConnectionAdapter connectionAdapter;
    private ConnectionManager connectionManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // Handle window insets
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeViews();
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        loadConnections();

        // Add entrance animations
        animateViewsOnCreate();
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        welcomeCard = findViewById(R.id.welcomeCard);
        cardNewConnection = findViewById(R.id.cardNewConnection);
        cardQueryEditor = findViewById(R.id.cardQueryEditor);
        recyclerViewConnections = findViewById(R.id.recyclerViewConnections);
        emptyStateLayout = findViewById(R.id.emptyStateLayout);
        tvRecentConnections = findViewById(R.id.tvRecentConnections);
        fabAddConnection = findViewById(R.id.fabAddConnection);

        connectionManager = ConnectionManager.getInstance(this);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("MySQL Manager");
        }
    }

    private void setupRecyclerView() {
        connectionAdapter = new ConnectionAdapter(this);
        connectionAdapter.setOnConnectionClickListener(this);

        recyclerViewConnections.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewConnections.setAdapter(connectionAdapter);

        // Add item animations
        recyclerViewConnections.setItemAnimator(new androidx.recyclerview.widget.DefaultItemAnimator());
    }

    private void setupClickListeners() {
        cardNewConnection.setOnClickListener(v -> {
            animateCardClick(cardNewConnection);
            openNewConnectionActivity();
        });

        cardQueryEditor.setOnClickListener(v -> {
            animateCardClick(cardQueryEditor);
            openQueryEditorActivity();
        });

        fabAddConnection.setOnClickListener(v -> {
            animateFabClick();
            openNewConnectionActivity();
        });
    }

    private void loadConnections() {
        List<DatabaseConnection> connections = connectionManager.getRecentConnections(10);

        if (connections.isEmpty()) {
            showEmptyState();
        } else {
            showConnectionsList();
            connectionAdapter.setConnections(connections);
        }
    }

    private void showEmptyState() {
        recyclerViewConnections.setVisibility(View.GONE);
        tvRecentConnections.setVisibility(View.GONE);
        emptyStateLayout.setVisibility(View.VISIBLE);

        // Animate empty state
        emptyStateLayout.setAlpha(0f);
        emptyStateLayout.animate()
                .alpha(1f)
                .setDuration(300)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void showConnectionsList() {
        emptyStateLayout.setVisibility(View.GONE);
        tvRecentConnections.setVisibility(View.VISIBLE);
        recyclerViewConnections.setVisibility(View.VISIBLE);
    }

    // Animation methods
    private void animateViewsOnCreate() {
        // Animate welcome card
        welcomeCard.setTranslationY(-100f);
        welcomeCard.setAlpha(0f);
        welcomeCard.animate()
                .translationY(0f)
                .alpha(1f)
                .setDuration(500)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();

        // Animate quick action cards
        cardNewConnection.setTranslationX(-100f);
        cardNewConnection.setAlpha(0f);
        cardNewConnection.animate()
                .translationX(0f)
                .alpha(1f)
                .setDuration(500)
                .setStartDelay(200)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();

        cardQueryEditor.setTranslationX(100f);
        cardQueryEditor.setAlpha(0f);
        cardQueryEditor.animate()
                .translationX(0f)
                .alpha(1f)
                .setDuration(500)
                .setStartDelay(300)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();

        // Animate FAB
        fabAddConnection.setScaleX(0f);
        fabAddConnection.setScaleY(0f);
        fabAddConnection.animate()
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(300)
                .setStartDelay(600)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void animateCardClick(View card) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(card, "scaleX", 1f, 0.95f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(card, "scaleY", 1f, 0.95f, 1f);
        scaleX.setDuration(150);
        scaleY.setDuration(150);
        scaleX.start();
        scaleY.start();
    }

    private void animateFabClick() {
        ObjectAnimator rotation = ObjectAnimator.ofFloat(fabAddConnection, "rotation", 0f, 45f, 0f);
        rotation.setDuration(200);
        rotation.start();
    }

    // Navigation methods
    private void openNewConnectionActivity() {
        // TODO: Implement connection activity
        Toast.makeText(this, "Opening New Connection...", Toast.LENGTH_SHORT).show();
    }

    private void openQueryEditorActivity() {
        // TODO: Implement query editor activity
        Toast.makeText(this, "Opening Query Editor...", Toast.LENGTH_SHORT).show();
    }

    // ConnectionAdapter.OnConnectionClickListener implementation
    @Override
    public void onConnectionClick(DatabaseConnection connection) {
        // Animate connection selection
        Toast.makeText(this, "Connecting to " + connection.getDisplayName(), Toast.LENGTH_SHORT).show();
        // TODO: Implement connection logic
    }

    @Override
    public void onConnectionMenuClick(DatabaseConnection connection, View view) {
        showConnectionMenu(connection, view);
    }

    private void showConnectionMenu(DatabaseConnection connection, View anchorView) {
        PopupMenu popup = new PopupMenu(this, anchorView);
        popup.getMenuInflater().inflate(R.menu.connection_menu, popup.getMenu());

        // Update favorite menu item
        MenuItem favoriteItem = popup.getMenu().findItem(R.id.action_toggle_favorite);
        if (favoriteItem != null) {
            favoriteItem.setTitle(connection.isFavorite() ? "Remove from Favorites" : "Add to Favorites");
        }

        popup.setOnMenuItemClickListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.action_connect) {
                onConnectionClick(connection);
                return true;
            } else if (itemId == R.id.action_edit) {
                editConnection(connection);
                return true;
            } else if (itemId == R.id.action_toggle_favorite) {
                toggleFavorite(connection);
                return true;
            } else if (itemId == R.id.action_duplicate) {
                duplicateConnection(connection);
                return true;
            } else if (itemId == R.id.action_delete) {
                deleteConnection(connection);
                return true;
            }
            return false;
        });

        popup.show();
    }

    private void editConnection(DatabaseConnection connection) {
        Toast.makeText(this, "Edit " + connection.getDisplayName(), Toast.LENGTH_SHORT).show();
        // TODO: Implement edit connection
    }

    private void toggleFavorite(DatabaseConnection connection) {
        connectionManager.toggleFavorite(connection.getId());
        connection.setFavorite(!connection.isFavorite());
        connectionAdapter.updateConnection(connection);

        String message = connection.isFavorite() ? "Added to favorites" : "Removed from favorites";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void duplicateConnection(DatabaseConnection connection) {
        DatabaseConnection duplicate = new DatabaseConnection();
        duplicate.setName(connection.getName() + " (Copy)");
        duplicate.setHost(connection.getHost());
        duplicate.setPort(connection.getPort());
        duplicate.setDatabase(connection.getDatabase());
        duplicate.setUsername(connection.getUsername());
        duplicate.setPassword(connection.getPassword());
        duplicate.setUseSSL(connection.isUseSSL());
        duplicate.setColor(connection.getColor());

        connectionManager.saveConnection(duplicate);
        connectionAdapter.addConnection(duplicate);

        Toast.makeText(this, "Connection duplicated", Toast.LENGTH_SHORT).show();
    }

    private void deleteConnection(DatabaseConnection connection) {
        connectionManager.deleteConnection(connection.getId());
        connectionAdapter.removeConnection(connection.getId());

        // Check if we need to show empty state
        if (connectionAdapter.getItemCount() == 0) {
            showEmptyState();
        }

        Toast.makeText(this, "Connection deleted", Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == R.id.action_search) {
            // TODO: Implement search
            Toast.makeText(this, "Search connections", Toast.LENGTH_SHORT).show();
            return true;
        } else if (itemId == R.id.action_refresh) {
            refreshConnections();
            return true;
        } else if (itemId == R.id.action_settings) {
            // TODO: Implement settings
            Toast.makeText(this, "Settings", Toast.LENGTH_SHORT).show();
            return true;
        } else if (itemId == R.id.action_import) {
            // TODO: Implement import
            Toast.makeText(this, "Import connections", Toast.LENGTH_SHORT).show();
            return true;
        } else if (itemId == R.id.action_export) {
            // TODO: Implement export
            Toast.makeText(this, "Export connections", Toast.LENGTH_SHORT).show();
            return true;
        } else if (itemId == R.id.action_about) {
            // TODO: Implement about dialog
            Toast.makeText(this, "About MySQL Manager", Toast.LENGTH_SHORT).show();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    private void refreshConnections() {
        // Animate refresh
        ObjectAnimator rotation = ObjectAnimator.ofFloat(toolbar.findViewById(R.id.action_refresh), "rotation", 0f, 360f);
        rotation.setDuration(500);
        rotation.start();

        loadConnections();
        Toast.makeText(this, "Connections refreshed", Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadConnections();
    }
}