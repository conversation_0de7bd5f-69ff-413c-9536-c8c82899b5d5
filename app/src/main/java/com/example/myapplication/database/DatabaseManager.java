package com.example.myapplication.database;

import android.util.Log;

import com.example.myapplication.models.DatabaseConnection;
import com.example.myapplication.models.QueryResult;
import com.example.myapplication.models.TableInfo;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Database manager for MySQL operations
 */
public class DatabaseManager {
    private static final String TAG = "DatabaseManager";
    private static DatabaseManager instance;
    private ExecutorService executorService;
    private Connection currentConnection;
    private DatabaseConnection currentConnectionConfig;
    
    private DatabaseManager() {
        executorService = Executors.newCachedThreadPool();
        try {
            // Load MySQL JDBC driver
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            Log.e(TAG, "MySQL JDBC driver not found", e);
        }
    }
    
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    /**
     * Test database connection
     */
    public interface ConnectionTestCallback {
        void onSuccess();
        void onError(String error);
    }
    
    public void testConnection(DatabaseConnection config, ConnectionTestCallback callback) {
        executorService.execute(() -> {
            try {
                Connection conn = DriverManager.getConnection(
                    config.getConnectionString(),
                    config.getUsername(),
                    config.getPassword()
                );
                conn.close();
                callback.onSuccess();
            } catch (SQLException e) {
                Log.e(TAG, "Connection test failed", e);
                callback.onError(e.getMessage());
            }
        });
    }
    
    /**
     * Connect to database
     */
    public interface ConnectionCallback {
        void onConnected();
        void onError(String error);
    }
    
    public void connect(DatabaseConnection config, ConnectionCallback callback) {
        executorService.execute(() -> {
            try {
                // Close existing connection
                disconnect();
                
                currentConnection = DriverManager.getConnection(
                    config.getConnectionString(),
                    config.getUsername(),
                    config.getPassword()
                );
                
                currentConnectionConfig = config;
                config.setLastConnected(System.currentTimeMillis());
                
                Log.d(TAG, "Connected to database: " + config.getDisplayName());
                callback.onConnected();
                
            } catch (SQLException e) {
                Log.e(TAG, "Failed to connect to database", e);
                callback.onError(e.getMessage());
            }
        });
    }
    
    /**
     * Disconnect from database
     */
    public void disconnect() {
        if (currentConnection != null) {
            try {
                currentConnection.close();
                Log.d(TAG, "Disconnected from database");
            } catch (SQLException e) {
                Log.e(TAG, "Error disconnecting from database", e);
            } finally {
                currentConnection = null;
                currentConnectionConfig = null;
            }
        }
    }
    
    /**
     * Execute SQL query
     */
    public interface QueryCallback {
        void onResult(QueryResult result);
        void onError(String error);
    }
    
    public void executeQuery(String sql, QueryCallback callback) {
        if (currentConnection == null) {
            callback.onError("No active database connection");
            return;
        }
        
        executorService.execute(() -> {
            QueryResult result = new QueryResult(sql);
            long startTime = System.currentTimeMillis();
            
            try {
                Statement statement = currentConnection.createStatement();
                
                if (sql.trim().toUpperCase().startsWith("SELECT") || 
                    sql.trim().toUpperCase().startsWith("SHOW") ||
                    sql.trim().toUpperCase().startsWith("DESCRIBE")) {
                    
                    // Execute query that returns results
                    ResultSet rs = statement.executeQuery(sql);
                    ResultSetMetaData metaData = rs.getMetaData();
                    
                    // Get column names
                    List<String> columnNames = new ArrayList<>();
                    for (int i = 1; i <= metaData.getColumnCount(); i++) {
                        columnNames.add(metaData.getColumnName(i));
                    }
                    result.setColumnNames(columnNames);
                    
                    // Get rows
                    while (rs.next()) {
                        List<Object> row = new ArrayList<>();
                        for (int i = 1; i <= metaData.getColumnCount(); i++) {
                            row.add(rs.getObject(i));
                        }
                        result.addRow(row);
                    }
                    
                    rs.close();
                } else {
                    // Execute update/insert/delete/etc
                    int affectedRows = statement.executeUpdate(sql);
                    result.setRowCount(affectedRows);
                }
                
                statement.close();
                result.setExecutionTime(System.currentTimeMillis() - startTime);
                callback.onResult(result);
                
            } catch (SQLException e) {
                Log.e(TAG, "Query execution failed", e);
                result.setErrorMessage(e.getMessage());
                result.setExecutionTime(System.currentTimeMillis() - startTime);
                callback.onResult(result);
            }
        });
    }
    
    /**
     * Get list of databases
     */
    public interface DatabaseListCallback {
        void onResult(List<String> databases);
        void onError(String error);
    }
    
    public void getDatabases(DatabaseListCallback callback) {
        executeQuery("SHOW DATABASES", new QueryCallback() {
            @Override
            public void onResult(QueryResult result) {
                if (result.isSuccess()) {
                    List<String> databases = new ArrayList<>();
                    for (List<Object> row : result.getRows()) {
                        if (row.size() > 0 && row.get(0) != null) {
                            databases.add(row.get(0).toString());
                        }
                    }
                    callback.onResult(databases);
                } else {
                    callback.onError(result.getErrorMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Get list of tables in current database
     */
    public interface TableListCallback {
        void onResult(List<String> tables);
        void onError(String error);
    }
    
    public void getTables(String database, TableListCallback callback) {
        String sql = database != null ? 
            "SHOW TABLES FROM `" + database + "`" : 
            "SHOW TABLES";
            
        executeQuery(sql, new QueryCallback() {
            @Override
            public void onResult(QueryResult result) {
                if (result.isSuccess()) {
                    List<String> tables = new ArrayList<>();
                    for (List<Object> row : result.getRows()) {
                        if (row.size() > 0 && row.get(0) != null) {
                            tables.add(row.get(0).toString());
                        }
                    }
                    callback.onResult(tables);
                } else {
                    callback.onError(result.getErrorMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    // Check if connected
    public boolean isConnected() {
        try {
            return currentConnection != null && !currentConnection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
    
    // Get current connection config
    public DatabaseConnection getCurrentConnectionConfig() {
        return currentConnectionConfig;
    }
    
    // Cleanup
    public void cleanup() {
        disconnect();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
