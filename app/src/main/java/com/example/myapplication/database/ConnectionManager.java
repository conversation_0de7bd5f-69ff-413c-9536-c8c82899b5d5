package com.example.myapplication.database;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;

import com.example.myapplication.models.DatabaseConnection;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Manager for storing and retrieving database connection profiles securely
 */
public class ConnectionManager {
    private static final String TAG = "ConnectionManager";
    private static final String PREFS_NAME = "database_connections";
    private static final String KEY_CONNECTIONS = "connections";
    
    private static ConnectionManager instance;
    private SharedPreferences encryptedPrefs;
    private Gson gson;
    
    private ConnectionManager(Context context) {
        gson = new Gson();
        initializeEncryptedPreferences(context);
    }
    
    public static synchronized ConnectionManager getInstance(Context context) {
        if (instance == null) {
            instance = new ConnectionManager(context.getApplicationContext());
        }
        return instance;
    }
    
    private void initializeEncryptedPreferences(Context context) {
        try {
            MasterKey masterKey = new MasterKey.Builder(context)
                    .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                    .build();
            
            encryptedPrefs = EncryptedSharedPreferences.create(
                    context,
                    PREFS_NAME,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
        } catch (GeneralSecurityException | IOException e) {
            Log.e(TAG, "Failed to create encrypted preferences", e);
            // Fallback to regular SharedPreferences (not recommended for production)
            encryptedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        }
    }
    
    /**
     * Save a database connection profile
     */
    public boolean saveConnection(DatabaseConnection connection) {
        try {
            List<DatabaseConnection> connections = getAllConnections();
            
            // Check if connection already exists (update) or add new
            boolean updated = false;
            for (int i = 0; i < connections.size(); i++) {
                if (connections.get(i).getId().equals(connection.getId())) {
                    connections.set(i, connection);
                    updated = true;
                    break;
                }
            }
            
            if (!updated) {
                // Generate ID if not set
                if (connection.getId() == null || connection.getId().isEmpty()) {
                    connection.setId("conn_" + System.currentTimeMillis());
                }
                connections.add(connection);
            }
            
            String json = gson.toJson(connections);
            encryptedPrefs.edit().putString(KEY_CONNECTIONS, json).apply();
            
            Log.d(TAG, "Connection saved: " + connection.getDisplayName());
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to save connection", e);
            return false;
        }
    }
    
    /**
     * Get all saved connections
     */
    public List<DatabaseConnection> getAllConnections() {
        try {
            String json = encryptedPrefs.getString(KEY_CONNECTIONS, "[]");
            Type listType = new TypeToken<List<DatabaseConnection>>(){}.getType();
            List<DatabaseConnection> connections = gson.fromJson(json, listType);
            return connections != null ? connections : new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "Failed to load connections", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * Get connection by ID
     */
    public DatabaseConnection getConnection(String id) {
        List<DatabaseConnection> connections = getAllConnections();
        for (DatabaseConnection connection : connections) {
            if (connection.getId().equals(id)) {
                return connection;
            }
        }
        return null;
    }
    
    /**
     * Delete a connection
     */
    public boolean deleteConnection(String id) {
        try {
            List<DatabaseConnection> connections = getAllConnections();
            // Use iterator to remove connection (compatible with API 21+)
            boolean removed = false;
            for (int i = connections.size() - 1; i >= 0; i--) {
                if (connections.get(i).getId().equals(id)) {
                    connections.remove(i);
                    removed = true;
                    break;
                }
            }
            
            String json = gson.toJson(connections);
            encryptedPrefs.edit().putString(KEY_CONNECTIONS, json).apply();
            
            Log.d(TAG, "Connection deleted: " + id);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to delete connection", e);
            return false;
        }
    }
    
    /**
     * Get favorite connections
     */
    public List<DatabaseConnection> getFavoriteConnections() {
        List<DatabaseConnection> allConnections = getAllConnections();
        List<DatabaseConnection> favorites = new ArrayList<>();
        
        for (DatabaseConnection connection : allConnections) {
            if (connection.isFavorite()) {
                favorites.add(connection);
            }
        }
        
        return favorites;
    }
    
    /**
     * Get recent connections (sorted by last connected time)
     */
    public List<DatabaseConnection> getRecentConnections(int limit) {
        List<DatabaseConnection> allConnections = getAllConnections();
        
        // Sort by last connected time (descending) - compatible with API 21+
        Collections.sort(allConnections, new Comparator<DatabaseConnection>() {
            @Override
            public int compare(DatabaseConnection a, DatabaseConnection b) {
                return Long.compare(b.getLastConnected(), a.getLastConnected());
            }
        });
        
        // Return limited list
        if (limit > 0 && allConnections.size() > limit) {
            return allConnections.subList(0, limit);
        }
        
        return allConnections;
    }
    
    /**
     * Update connection's last connected time
     */
    public void updateLastConnected(String id) {
        DatabaseConnection connection = getConnection(id);
        if (connection != null) {
            connection.setLastConnected(System.currentTimeMillis());
            saveConnection(connection);
        }
    }
    
    /**
     * Toggle favorite status
     */
    public boolean toggleFavorite(String id) {
        DatabaseConnection connection = getConnection(id);
        if (connection != null) {
            connection.setFavorite(!connection.isFavorite());
            return saveConnection(connection);
        }
        return false;
    }
    
    /**
     * Clear all connections
     */
    public void clearAllConnections() {
        encryptedPrefs.edit().remove(KEY_CONNECTIONS).apply();
        Log.d(TAG, "All connections cleared");
    }
    
    /**
     * Export connections to JSON (without passwords for security)
     */
    public String exportConnections(boolean includePasswords) {
        List<DatabaseConnection> connections = getAllConnections();
        
        if (!includePasswords) {
            // Create copies without passwords
            List<DatabaseConnection> safeConnections = new ArrayList<>();
            for (DatabaseConnection conn : connections) {
                DatabaseConnection safeConn = new DatabaseConnection();
                safeConn.setId(conn.getId());
                safeConn.setName(conn.getName());
                safeConn.setHost(conn.getHost());
                safeConn.setPort(conn.getPort());
                safeConn.setDatabase(conn.getDatabase());
                safeConn.setUsername(conn.getUsername());
                safeConn.setPassword(""); // Remove password
                safeConn.setUseSSL(conn.isUseSSL());
                safeConn.setFavorite(conn.isFavorite());
                safeConn.setColor(conn.getColor());
                safeConnections.add(safeConn);
            }
            return gson.toJson(safeConnections);
        }
        
        return gson.toJson(connections);
    }
    
    /**
     * Import connections from JSON
     */
    public boolean importConnections(String json, boolean replaceExisting) {
        try {
            Type listType = new TypeToken<List<DatabaseConnection>>(){}.getType();
            List<DatabaseConnection> importedConnections = gson.fromJson(json, listType);
            
            if (importedConnections == null) {
                return false;
            }
            
            List<DatabaseConnection> existingConnections = replaceExisting ? 
                new ArrayList<>() : getAllConnections();
            
            // Add imported connections
            for (DatabaseConnection conn : importedConnections) {
                // Generate new ID to avoid conflicts
                conn.setId("conn_" + System.currentTimeMillis() + "_" + conn.hashCode());
                existingConnections.add(conn);
            }
            
            String finalJson = gson.toJson(existingConnections);
            encryptedPrefs.edit().putString(KEY_CONNECTIONS, finalJson).apply();
            
            Log.d(TAG, "Imported " + importedConnections.size() + " connections");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to import connections", e);
            return false;
        }
    }
}
