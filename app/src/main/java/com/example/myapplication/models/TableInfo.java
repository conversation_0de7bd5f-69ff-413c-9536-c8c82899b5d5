package com.example.myapplication.models;

import java.util.ArrayList;
import java.util.List;

/**
 * Model class representing database table information
 */
public class TableInfo {
    private String tableName;
    private String tableSchema;
    private String tableType; // TABLE, VIEW, etc.
    private long rowCount;
    private String engine;
    private String collation;
    private String comment;
    private long dataLength;
    private long indexLength;
    private List<ColumnInfo> columns;
    private List<IndexInfo> indexes;
    
    public TableInfo() {
        this.columns = new ArrayList<>();
        this.indexes = new ArrayList<>();
    }
    
    public TableInfo(String tableName, String tableSchema) {
        this();
        this.tableName = tableName;
        this.tableSchema = tableSchema;
    }
    
    // Inner class for column information
    public static class ColumnInfo {
        private String columnName;
        private String dataType;
        private boolean isNullable;
        private String defaultValue;
        private boolean isPrimaryKey;
        private boolean isAutoIncrement;
        private String comment;
        private int ordinalPosition;
        private String columnKey; // PRI, UNI, MUL
        
        public ColumnInfo() {}
        
        public ColumnInfo(String columnName, String dataType, boolean isNullable) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.isNullable = isNullable;
        }
        
        // Getters and Setters
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }
        
        public boolean isNullable() { return isNullable; }
        public void setNullable(boolean nullable) { isNullable = nullable; }
        
        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }
        
        public boolean isPrimaryKey() { return isPrimaryKey; }
        public void setPrimaryKey(boolean primaryKey) { isPrimaryKey = primaryKey; }
        
        public boolean isAutoIncrement() { return isAutoIncrement; }
        public void setAutoIncrement(boolean autoIncrement) { isAutoIncrement = autoIncrement; }
        
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        
        public int getOrdinalPosition() { return ordinalPosition; }
        public void setOrdinalPosition(int ordinalPosition) { this.ordinalPosition = ordinalPosition; }
        
        public String getColumnKey() { return columnKey; }
        public void setColumnKey(String columnKey) { this.columnKey = columnKey; }
        
        @Override
        public String toString() {
            return columnName + " " + dataType + (isNullable ? "" : " NOT NULL");
        }
    }
    
    // Inner class for index information
    public static class IndexInfo {
        private String indexName;
        private boolean isUnique;
        private String columnName;
        private int seqInIndex;
        private String indexType;
        
        public IndexInfo() {}
        
        public IndexInfo(String indexName, String columnName, boolean isUnique) {
            this.indexName = indexName;
            this.columnName = columnName;
            this.isUnique = isUnique;
        }
        
        // Getters and Setters
        public String getIndexName() { return indexName; }
        public void setIndexName(String indexName) { this.indexName = indexName; }
        
        public boolean isUnique() { return isUnique; }
        public void setUnique(boolean unique) { isUnique = unique; }
        
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        
        public int getSeqInIndex() { return seqInIndex; }
        public void setSeqInIndex(int seqInIndex) { this.seqInIndex = seqInIndex; }
        
        public String getIndexType() { return indexType; }
        public void setIndexType(String indexType) { this.indexType = indexType; }
        
        @Override
        public String toString() {
            return indexName + " (" + columnName + ")" + (isUnique ? " UNIQUE" : "");
        }
    }
    
    // Add column to table
    public void addColumn(ColumnInfo column) {
        this.columns.add(column);
    }
    
    // Add index to table
    public void addIndex(IndexInfo index) {
        this.indexes.add(index);
    }
    
    // Get primary key columns
    public List<ColumnInfo> getPrimaryKeyColumns() {
        List<ColumnInfo> pkColumns = new ArrayList<>();
        for (ColumnInfo column : columns) {
            if (column.isPrimaryKey()) {
                pkColumns.add(column);
            }
        }
        return pkColumns;
    }
    
    // Get formatted table size
    public String getFormattedSize() {
        long totalSize = dataLength + indexLength;
        if (totalSize < 1024) {
            return totalSize + " B";
        } else if (totalSize < 1024 * 1024) {
            return String.format("%.1f KB", totalSize / 1024.0);
        } else if (totalSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", totalSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", totalSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    // Getters and Setters
    public String getTableName() { return tableName; }
    public void setTableName(String tableName) { this.tableName = tableName; }
    
    public String getTableSchema() { return tableSchema; }
    public void setTableSchema(String tableSchema) { this.tableSchema = tableSchema; }
    
    public String getTableType() { return tableType; }
    public void setTableType(String tableType) { this.tableType = tableType; }
    
    public long getRowCount() { return rowCount; }
    public void setRowCount(long rowCount) { this.rowCount = rowCount; }
    
    public String getEngine() { return engine; }
    public void setEngine(String engine) { this.engine = engine; }
    
    public String getCollation() { return collation; }
    public void setCollation(String collation) { this.collation = collation; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    
    public long getDataLength() { return dataLength; }
    public void setDataLength(long dataLength) { this.dataLength = dataLength; }
    
    public long getIndexLength() { return indexLength; }
    public void setIndexLength(long indexLength) { this.indexLength = indexLength; }
    
    public List<ColumnInfo> getColumns() { return columns; }
    public void setColumns(List<ColumnInfo> columns) { this.columns = columns; }
    
    public List<IndexInfo> getIndexes() { return indexes; }
    public void setIndexes(List<IndexInfo> indexes) { this.indexes = indexes; }
    
    @Override
    public String toString() {
        return "TableInfo{" +
                "tableName='" + tableName + '\'' +
                ", tableType='" + tableType + '\'' +
                ", rowCount=" + rowCount +
                ", columnCount=" + columns.size() +
                '}';
    }
}
