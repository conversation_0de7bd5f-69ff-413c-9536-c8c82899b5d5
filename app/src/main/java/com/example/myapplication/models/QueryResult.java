package com.example.myapplication.models;

import java.util.ArrayList;
import java.util.List;

/**
 * Model class to hold SQL query results
 */
public class QueryResult {
    private List<String> columnNames;
    private List<List<Object>> rows;
    private int rowCount;
    private long executionTime;
    private String query;
    private boolean isSuccess;
    private String errorMessage;
    private QueryType queryType;
    
    public enum QueryType {
        SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, SHOW, DESCRIBE, OTHER
    }
    
    public QueryResult() {
        this.columnNames = new ArrayList<>();
        this.rows = new ArrayList<>();
        this.isSuccess = true;
    }
    
    public QueryResult(String query) {
        this();
        this.query = query;
        this.queryType = determineQueryType(query);
    }
    
    // Determine query type from SQL string
    private QueryType determineQueryType(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return QueryType.OTHER;
        }
        
        String upperSql = sql.trim().toUpperCase();
        if (upperSql.startsWith("SELECT")) return QueryType.SELECT;
        if (upperSql.startsWith("INSERT")) return QueryType.INSERT;
        if (upperSql.startsWith("UPDATE")) return QueryType.UPDATE;
        if (upperSql.startsWith("DELETE")) return QueryType.DELETE;
        if (upperSql.startsWith("CREATE")) return QueryType.CREATE;
        if (upperSql.startsWith("DROP")) return QueryType.DROP;
        if (upperSql.startsWith("ALTER")) return QueryType.ALTER;
        if (upperSql.startsWith("SHOW")) return QueryType.SHOW;
        if (upperSql.startsWith("DESCRIBE") || upperSql.startsWith("DESC")) return QueryType.DESCRIBE;
        
        return QueryType.OTHER;
    }
    
    // Add a row of data
    public void addRow(List<Object> row) {
        this.rows.add(new ArrayList<>(row));
        this.rowCount = this.rows.size();
    }
    
    // Add column names
    public void setColumnNames(List<String> columnNames) {
        this.columnNames = new ArrayList<>(columnNames);
    }
    
    // Get value at specific row and column
    public Object getValue(int row, int column) {
        if (row >= 0 && row < rows.size() && column >= 0 && column < columnNames.size()) {
            List<Object> rowData = rows.get(row);
            if (column < rowData.size()) {
                return rowData.get(column);
            }
        }
        return null;
    }
    
    // Get value by column name
    public Object getValue(int row, String columnName) {
        int columnIndex = columnNames.indexOf(columnName);
        if (columnIndex >= 0) {
            return getValue(row, columnIndex);
        }
        return null;
    }
    
    // Check if result has data
    public boolean hasData() {
        return !rows.isEmpty() && !columnNames.isEmpty();
    }
    
    // Get formatted execution time
    public String getFormattedExecutionTime() {
        if (executionTime < 1000) {
            return executionTime + " ms";
        } else {
            return String.format("%.2f s", executionTime / 1000.0);
        }
    }
    
    // Getters and Setters
    public List<String> getColumnNames() {
        return columnNames;
    }
    
    public List<List<Object>> getRows() {
        return rows;
    }
    
    public int getRowCount() {
        return rowCount;
    }
    
    public void setRowCount(int rowCount) {
        this.rowCount = rowCount;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
        this.queryType = determineQueryType(query);
    }
    
    public boolean isSuccess() {
        return isSuccess;
    }
    
    public void setSuccess(boolean success) {
        isSuccess = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.isSuccess = false;
    }
    
    public QueryType getQueryType() {
        return queryType;
    }
    
    public void setQueryType(QueryType queryType) {
        this.queryType = queryType;
    }
    
    // Get column count
    public int getColumnCount() {
        return columnNames.size();
    }
    
    // Clear all data
    public void clear() {
        columnNames.clear();
        rows.clear();
        rowCount = 0;
        executionTime = 0;
        isSuccess = true;
        errorMessage = null;
    }
    
    @Override
    public String toString() {
        return "QueryResult{" +
                "columnCount=" + columnNames.size() +
                ", rowCount=" + rowCount +
                ", executionTime=" + executionTime +
                ", queryType=" + queryType +
                ", isSuccess=" + isSuccess +
                '}';
    }
}
