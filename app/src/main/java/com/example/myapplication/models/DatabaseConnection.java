package com.example.myapplication.models;

import java.io.Serializable;
import java.util.Objects;

/**
 * Model class representing a MySQL database connection configuration
 */
public class DatabaseConnection implements Serializable {
    private String id;
    private String name;
    private String host;
    private int port;
    private String database;
    private String username;
    private String password;
    private boolean useSSL;
    private String connectionString;
    private long lastConnected;
    private boolean isFavorite;
    private String color; // For UI theming
    
    // Default constructor
    public DatabaseConnection() {
        this.port = 3306; // Default MySQL port
        this.useSSL = false;
        this.isFavorite = false;
        this.color = "#2196F3"; // Default blue color
    }
    
    // Constructor with basic parameters
    public DatabaseConnection(String name, String host, int port, String database, 
                            String username, String password) {
        this();
        this.name = name;
        this.host = host;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
        this.id = generateId();
    }
    
    // Generate unique ID for connection
    private String generateId() {
        return "conn_" + System.currentTimeMillis() + "_" + hashCode();
    }
    
    // Build connection string
    public String buildConnectionString() {
        StringBuilder sb = new StringBuilder();
        sb.append("jdbc:mysql://")
          .append(host)
          .append(":")
          .append(port)
          .append("/")
          .append(database != null ? database : "")
          .append("?useSSL=").append(useSSL)
          .append("&allowPublicKeyRetrieval=true")
          .append("&serverTimezone=UTC");
        
        this.connectionString = sb.toString();
        return connectionString;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getDatabase() {
        return database;
    }
    
    public void setDatabase(String database) {
        this.database = database;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public boolean isUseSSL() {
        return useSSL;
    }
    
    public void setUseSSL(boolean useSSL) {
        this.useSSL = useSSL;
    }
    
    public String getConnectionString() {
        if (connectionString == null) {
            buildConnectionString();
        }
        return connectionString;
    }
    
    public void setConnectionString(String connectionString) {
        this.connectionString = connectionString;
    }
    
    public long getLastConnected() {
        return lastConnected;
    }
    
    public void setLastConnected(long lastConnected) {
        this.lastConnected = lastConnected;
    }
    
    public boolean isFavorite() {
        return isFavorite;
    }
    
    public void setFavorite(boolean favorite) {
        isFavorite = favorite;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    // Utility methods
    public String getDisplayName() {
        return name != null && !name.isEmpty() ? name : host + ":" + port;
    }
    
    public boolean isValid() {
        return host != null && !host.isEmpty() && 
               username != null && !username.isEmpty() &&
               port > 0 && port <= 65535;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DatabaseConnection that = (DatabaseConnection) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(host, port, database, username);
    }
    
    @Override
    public String toString() {
        return "DatabaseConnection{" +
                "name='" + name + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", database='" + database + '\'' +
                ", username='" + username + '\'' +
                ", useSSL=" + useSSL +
                '}';
    }
}
