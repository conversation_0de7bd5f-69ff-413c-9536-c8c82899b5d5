package com.example.myapplication.utils;

import com.example.myapplication.models.DatabaseConnection;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to generate sample database connections for testing
 */
public class SampleDataGenerator {
    
    public static List<DatabaseConnection> generateSampleConnections() {
        List<DatabaseConnection> connections = new ArrayList<>();
        
        // Production Database
        DatabaseConnection prod = new DatabaseConnection();
        prod.setId("conn_prod_001");
        prod.setName("Production Database");
        prod.setHost("prod-mysql.company.com");
        prod.setPort(3306);
        prod.setDatabase("production_db");
        prod.setUsername("prod_user");
        prod.setPassword("prod_password");
        prod.setUseSSL(true);
        prod.setFavorite(true);
        prod.setColor("#F44336"); // Red for production
        prod.setLastConnected(System.currentTimeMillis() - 3600000); // 1 hour ago
        connections.add(prod);
        
        // Development Database
        DatabaseConnection dev = new DatabaseConnection();
        dev.setId("conn_dev_002");
        dev.setName("Development Database");
        dev.setHost("dev-mysql.company.com");
        dev.setPort(3306);
        dev.setDatabase("dev_db");
        dev.setUsername("dev_user");
        dev.setPassword("dev_password");
        dev.setUseSSL(false);
        dev.setFavorite(false);
        dev.setColor("#4CAF50"); // Green for development
        dev.setLastConnected(System.currentTimeMillis() - 7200000); // 2 hours ago
        connections.add(dev);
        
        // Testing Database
        DatabaseConnection test = new DatabaseConnection();
        test.setId("conn_test_003");
        test.setName("Testing Database");
        test.setHost("test-mysql.company.com");
        test.setPort(3306);
        test.setDatabase("test_db");
        test.setUsername("test_user");
        test.setPassword("test_password");
        test.setUseSSL(false);
        test.setFavorite(true);
        test.setColor("#FF9800"); // Orange for testing
        test.setLastConnected(System.currentTimeMillis() - 86400000); // 1 day ago
        connections.add(test);
        
        // Local Database
        DatabaseConnection local = new DatabaseConnection();
        local.setId("conn_local_004");
        local.setName("Local MySQL");
        local.setHost("localhost");
        local.setPort(3306);
        local.setDatabase("local_db");
        local.setUsername("root");
        local.setPassword("password");
        local.setUseSSL(false);
        local.setFavorite(false);
        local.setColor("#2196F3"); // Blue for local
        local.setLastConnected(System.currentTimeMillis() - 1800000); // 30 minutes ago
        connections.add(local);
        
        // Analytics Database
        DatabaseConnection analytics = new DatabaseConnection();
        analytics.setId("conn_analytics_005");
        analytics.setName("Analytics Database");
        analytics.setHost("analytics-mysql.company.com");
        analytics.setPort(3306);
        analytics.setDatabase("analytics_db");
        analytics.setUsername("analytics_user");
        analytics.setPassword("analytics_password");
        analytics.setUseSSL(true);
        analytics.setFavorite(false);
        analytics.setColor("#9C27B0"); // Purple for analytics
        analytics.setLastConnected(System.currentTimeMillis() - *********); // 2 days ago
        connections.add(analytics);
        
        return connections;
    }
    
    public static DatabaseConnection createSampleConnection(String name, String host, String database) {
        DatabaseConnection connection = new DatabaseConnection();
        connection.setName(name);
        connection.setHost(host);
        connection.setPort(3306);
        connection.setDatabase(database);
        connection.setUsername("user");
        connection.setPassword("password");
        connection.setUseSSL(false);
        connection.setFavorite(false);
        connection.setColor("#2196F3");
        connection.setLastConnected(System.currentTimeMillis());
        return connection;
    }
}
