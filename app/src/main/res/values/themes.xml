<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.MyApplication" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Secondary colors -->
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/text_on_primary</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/surface_color</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Window properties -->
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:windowAnimationStyle">@style/WindowAnimationTransition</item>

        <!-- Material Design 3 -->
        <item name="materialAlertDialogTheme">@style/CustomAlertDialog</item>
        <item name="bottomSheetDialogTheme">@style/CustomBottomSheetDialog</item>
    </style>

    <style name="Theme.MyApplication" parent="Base.Theme.MyApplication" />

    <!-- Custom Alert Dialog Theme -->
    <style name="CustomAlertDialog" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:background">@color/surface_color</item>
    </style>

    <!-- Custom Bottom Sheet Dialog Theme -->
    <style name="CustomBottomSheetDialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:background">@color/surface_color</item>
    </style>

    <!-- Window Animation -->
    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>

    <!-- Card Styles -->
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="strokeWidth">0dp</item>
    </style>

    <!-- Button Styles -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary_color</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_color</item>
        <item name="android:textColor">@color/primary_color</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <!-- Text Styles -->
    <style name="HeadlineText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SubheadText">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="CaptionText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <!-- Toolbar Style -->
    <style name="ToolbarStyle" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/primary_color</item>
        <item name="titleTextColor">@color/text_on_primary</item>
        <item name="subtitleTextColor">@color/text_on_primary</item>
        <item name="android:theme">@style/ThemeOverlay.Material3.Dark</item>
    </style>
</resources>