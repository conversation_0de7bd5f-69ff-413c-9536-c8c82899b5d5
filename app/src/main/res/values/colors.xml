<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Primary colors -->
    <color name="primary_color">#1976D2</color>
    <color name="primary_dark">#1565C0</color>
    <color name="primary_light">#42A5F5</color>

    <!-- Secondary colors -->
    <color name="secondary_color">#FF5722</color>
    <color name="secondary_dark">#E64A19</color>
    <color name="secondary_light">#FF8A65</color>

    <!-- Background colors -->
    <color name="background_color">#F5F5F5</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="card_background">#FFFFFF</color>

    <!-- Text colors -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_on_primary">#FFFFFF</color>

    <!-- Status colors -->
    <color name="success_color">#4CAF50</color>
    <color name="error_color">#F44336</color>
    <color name="warning_color">#FF9800</color>
    <color name="info_color">#2196F3</color>

    <!-- Connection status colors -->
    <color name="connected_color">#4CAF50</color>
    <color name="disconnected_color">#9E9E9E</color>
    <color name="connecting_color">#FF9800</color>

    <!-- Database type colors -->
    <color name="mysql_color">#00758F</color>
    <color name="table_color">#795548</color>
    <color name="view_color">#9C27B0</color>

    <!-- Divider and border colors -->
    <color name="divider_color">#E0E0E0</color>
    <color name="border_color">#CCCCCC</color>

    <!-- Overlay colors -->
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#80FFFFFF</color>

    <!-- Ripple effect -->
    <color name="ripple_color">#1F000000</color>
</resources>