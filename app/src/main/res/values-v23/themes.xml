<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme for API 23+ -->
    <style name="Base.Theme.MyApplication" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Secondary colors -->
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/text_on_primary</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_color</item>
        <item name="colorSurface">@color/surface_color</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status bar (API 23+) -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar (API 23+) -->
        <item name="android:navigationBarColor">@color/surface_color</item>

        <!-- Window properties -->
        <item name="android:windowBackground">@color/background_color</item>
        <item name="android:windowAnimationStyle">@style/WindowAnimationTransition</item>

        <!-- Material Design 3 -->
        <item name="materialAlertDialogTheme">@style/CustomAlertDialog</item>
        <item name="bottomSheetDialogTheme">@style/CustomBottomSheetDialog</item>
    </style>
</resources>
