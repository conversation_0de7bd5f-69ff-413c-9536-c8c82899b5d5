<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardConnection"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Connection Color Indicator -->
        <View
            android:id="@+id/colorIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="16dp"
            android:background="@color/primary_color" />

        <!-- Connection Icon -->
        <ImageView
            android:id="@+id/ivConnectionIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background"
            android:padding="8dp"
            android:src="@drawable/ic_database"
            app:tint="@color/primary_color" />

        <!-- Connection Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvConnectionName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="Production Database" />

                <ImageView
                    android:id="@+id/ivFavorite"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_favorite"
                    app:tint="@color/warning_color"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvConnectionDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="mysql://localhost:3306/mydb" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- Connection Status -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:id="@+id/statusIndicator"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginEnd="6dp"
                        android:background="@drawable/circle_background"
                        android:backgroundTint="@color/disconnected_color" />

                    <TextView
                        android:id="@+id/tvConnectionStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        tools:text="Disconnected" />

                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- Last Connected -->
                <TextView
                    android:id="@+id/tvLastConnected"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    tools:text="2 hours ago" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Menu -->
        <ImageView
            android:id="@+id/ivMenuAction"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_more_vert"
            app:tint="@color/text_secondary" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
