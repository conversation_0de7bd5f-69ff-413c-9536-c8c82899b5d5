<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Color Indicator Placeholder -->
        <View
            android:layout_width="4dp"
            android:layout_height="60dp"
            android:layout_marginEnd="16dp"
            android:background="@color/divider_color" />

        <!-- Icon Placeholder -->
        <View
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/divider_color" />

        <!-- Content Placeholder -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title Placeholder -->
            <View
                android:layout_width="150dp"
                android:layout_height="16dp"
                android:background="@color/divider_color" />

            <!-- Subtitle Placeholder -->
            <View
                android:layout_width="200dp"
                android:layout_height="12dp"
                android:layout_marginTop="8dp"
                android:background="@color/divider_color" />

            <!-- Status Placeholder -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <View
                    android:layout_width="80dp"
                    android:layout_height="10dp"
                    android:background="@color/divider_color" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <View
                    android:layout_width="60dp"
                    android:layout_height="10dp"
                    android:background="@color/divider_color" />

            </LinearLayout>

        </LinearLayout>

        <!-- Menu Placeholder -->
        <View
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:background="@color/divider_color" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
