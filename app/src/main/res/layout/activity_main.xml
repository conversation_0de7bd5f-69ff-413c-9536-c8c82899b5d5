<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color"
    tools:context=".MainActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="4dp"
        app:elevation="4dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_color"
            app:title="MySQL Manager"
            app:titleTextColor="@android:color/white"
            app:menu="@menu/main_menu" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Welcome Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/welcomeCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_database"
                        app:tint="@color/primary_color" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="MySQL Database Manager"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="8dp"
                        android:text="Professional database management tool"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Quick Actions -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Quick Actions"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="24dp">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardNewConnection"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_add"
                            android:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="New Connection"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/cardQueryEditor"
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginBottom="8dp"
                            android:src="@drawable/ic_code"
                            android:tint="@color/primary_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Query Editor"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:gravity="center" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Recent Connections -->
            <TextView
                android:id="@+id/tvRecentConnections"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Recent Connections"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewConnections"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                tools:listitem="@layout/item_connection" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_database_off"
                    android:alpha="0.5"
                    android:tint="@color/text_secondary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No connections yet"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Create your first database connection to get started"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddConnection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@android:color/white"
        app:backgroundTint="@color/primary_color" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>